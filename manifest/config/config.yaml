
# 应用运行时设置
app:
  # 开发模式切换 true: Polling, false: Webhook
  devMode: true
  # 开发特定设置
  development:
    # true: Gateway 直接调用 Processor 逻辑
    # false: Gateway 通过 Kafka 发送消息给 Processor (生产模式)
    directProcessingMode: true # 暂定 true 使用拉取模式

i18n:
  path: "manifest/i18n"
  language: "zh-CN" # 默认语言


telegram:
  redPacketDefaultCoverFileId: "AgACAgUAAxkBAAIItmgE5ubBTvvnZNL7J52mkTbvrUPXAALswjEbBuEoVNFkXNv1qiWmAQADAgADeQADNgQ"
  redPacketDefaultCoverImageURL: "https://github.com/yalks/geetest-img/blob/main/photo_2025-04-20_20-21-58.jpg?raw=true"
  webhook:
    host: ""
    port: 8443
    path: "/webhook"
    secret: ""
    cert_path: ""
    key_path: ""

  # API 超时配置 (单位: 秒)
  api:
    inlineQueryTimeoutSeconds: 10       # inline query API 调用超时时间 (默认10秒)
    generalTimeoutSeconds: 30           # 一般 API 调用超时时间 (默认30秒)

database:
  logger:
    path: "logs/database" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    level: "all"
    stdout: true
  default: # 使用 MySQL
    link: "mysql:root:root@tcp(127.0.0.1:3306)/game?loc=Local&parseTime=true&charset=utf8mb4&timeout=30s&readTimeout=30s&writeTimeout=30s" # 添加连接超时设置
    debug: true # 开发环境建议开启
    charset: utf8mb4
    dryRun: false
    maxIdle: 20          # 适中的空闲连接数
    maxOpen: 80          # 为bot-api分配80个连接(多系统共享500个)
    maxLifetime: "600s"  # 10分钟连接生命周期
    connMaxIdleTime: "300s"  # 5分钟空闲超时


kafka:
  brokers:
    - "localhost:9092" # 本地 Kafka Broker 地址
  dev: true # 用于控制诊断日志输出 (本地开发环境启用)
  producerConfig: # Kafka 生产者配置 (本地 Kafka)
    dialTimeout: 3s
    writeTimeout: 5s
    readTimeout: 5s
    flushMessages: 100
    flushFrequency: 10ms
    retryMax: 3
    # SASL 认证配置 (本地 Kafka PLAIN 认证)
    saslEnable: true # 启用 SASL 认证
    saslMechanism: "PLAIN" # 本地 Kafka 使用 PLAIN 认证
    saslUser: "user" # 本地 Kafka 用户名
    saslPassword: "password" # 本地 Kafka 密码
    # TLS 配置 (本地开发环境禁用)
    tlsEnable: false # 本地环境不使用 TLS
    tlsInsecureSkipVerify: false # 本地环境不需要
  consumerConfig: # Kafka 消费者配置 (本地 Kafka)
    groupId: "telegram_processor_group" # 消费者组 ID
    redPacketClaimGroupId: "red_packet_claim_processor_group" # 红包领取消费者组 ID
    redPacketMessageUpdaterGroupId: "red_packet_message_updater_group" # 红包消息更新消费者组 ID
    unifiedNotifierGroupId: "unified_notifier_group" # 新的统一通知消费者组 ID
    userSyncGroupId: "user_sync_processor_group" # 用户同步消费者组 ID
    dialTimeout: 3s
    fetchMin: 10000
    fetchDefault: 10000000
    maxWaitTime: 10ms
    autoCommitInterval: 50ms
    retryBackoff: 500ms
    sessionTimeout: 10s    # 从20秒减少到10秒，加快重平衡检测
    heartbeatInterval: 2s  # 从3秒减少到2秒，更频繁的心跳
    # SASL 认证配置 (本地 Kafka PLAIN 认证)
    saslEnable: true # 启用 SASL 认证
    saslMechanism: "PLAIN" # 本地 Kafka 使用 PLAIN 认证
    saslUser: "user" # 本地 Kafka 用户名
    saslPassword: "password" # 本地 Kafka 密码
    # TLS 配置 (本地开发环境禁用)
    tlsEnable: false # 本地环境不使用 TLS
    tlsInsecureSkipVerify: false # 本地环境不需要
  topics:
    incoming: "telegram_incoming_messages"
    outgoing: "telegram_outgoing_replies"
    redPacketClaimRequests: "red_packet_claim_requests" # 红包领取请求主题
    redPacketMessageUpdateRequests: "red_packet_message_update_requests" # 红包消息更新请求主题
    unifiedNotifications: "unified_notifications" # 统一通知主题 (推荐使用)
    userSyncUpdates: "user_sync_updates" # 用户同步更新主题
    # 主题默认配置
    defaultPartitions: 3 # 默认分区数
    defaultReplicationFactor: 1 # 默认副本数 (本地单节点 Kafka 使用 1)
    defaultRetentionMs: "604800000" # 默认保留时间 (7天)
    # 不同类型主题的分区配置
    messagePartitions: 5 # 消息主题分区数 (需要更高吞吐量)
    notificationPartitions: 2 # 通知主题分区数
redis:
  default:
    address: "127.0.0.1:6379" # 确认或修改为您的 Redis 地址
    db: 0 # 确认或修改 DB 编号
    pass: "valkey_password"
    idleTimeout: "60s"
    maxConnLifetime: "90s"
    waitTimeout: "60s"
    dialTimeout: "30s"
    readTimeout: "30s"
    writeTimeout: "30s"
    maxActive: 100

logger:
  path: "./logs/" # Log file directory path
  file: "{Y-m-d}.log" # Log file name format

# 价格监控服务配置
priceMonitor:
  service:
    name: "price-monitor-service"
    port: 8900
    logLevel: "debug"
  
  websocket:
    providers:
      binance:
        url: "wss://stream.binance.com:9443"
        reconnectDelay: "5s"
        pingInterval: "30s"
        pongTimeout: "10s"
        enabled: true
  
  redis:
    configName: "default"  # 使用默认Redis配置
    poolSize: 10
    maxRetries: 3
  
  symbols:
    - "ETHUSDT"
    - "TRXUSDT"
  
  # C2C 法币价格配置
  c2c:
    enabled: true                    # 是否启用C2C法币价格获取
    updateInterval: "30s"            # 价格更新间隔 (可配置: 10s, 30s, 1m, 5m等)
    timeout: "10s"                   # API请求超时时间
    maxRetries: 3                    # 最大重试次数
  
  # 法币交易对配置
  fiatPairs:
    - asset: "USDT"                  # 资产符号
      fiatCurrency: "CNY"            # 法币符号
      enabled: true                  # 是否启用此交易对
    - asset: "USDT"                  # 可以添加更多法币支持
      fiatCurrency: "USD"            # 美元
      enabled: true                 # 暂时禁用
  
  validation:
    maxPriceChange: 0.1              # 10% 最大价格变动
    minUpdateInterval: "5s"          # 最小更新间隔
    staleDataThreshold: "30s"        # 过期数据阈值
  
  prefix: "" # Log content prefix
  level: "debug" # Log output level
  ctxKeys: ["cmd"] # Context variable names
  header: true # Print log header information
  stSkip: 0
  stdout: true # Output to terminal simultaneously
  rotateSize: 0 # File size based rotation (0 means disabled)
  rotateExpire: "24h" # Rotate logs every 24 hours
  rotateBackupLimit: 3 # Keep last 3 days of log files
  rotateBackupExpire: "72h" # Delete log files older than 72 hours (3 days)
  rotateBackupCompress: 0 # No compression
  rotateCheckInterval: "1h" # Check rotation every hour
  stdoutColorDisabled: false # Enable terminal color output
  writerColorEnable: false # Disable color in log files
  flags: 92

# 统一对象存储配置
storage:
  provider: "s3" # 指定存储提供商: "minio" 或 "s3"
  # AWS S3 配置
  s3:
    accessKeyID: "********************" # AWS Access Key ID
    secretAccessKey: "jRlPC3qDWMNvW7IX8MThWEalYSxeLupBcUKZCQHO" # AWS Secret Access Key
    region: "ap-northeast-1" # AWS Region
    bucketName: "yescex1" # AWS Bucket Name
    usePathStyleEndpoint: false # Whether to use path style endpoint
    publicURLPrefix: "" # (可选) 用于构建文件访问 URL 的前缀，如果为空，则使用 S3 默认 URL 格式

# 红包相关配置
red_packet:
  default_token_symbol: "CNY"           # 默认红包代币符号

# 统一超时配置 (单位: 分钟)
timeouts:
  redPacketExpirationMinutes: 1440      # 红包过期时间 (默认24小时 = 1440分钟)
  transferExpirationMinutes: 1440       # 转账过期时间 (默认24小时 = 1440分钟)
  paymentRequestExpirationMinutes: 1440 # 收款过期时间 (默认24小时 = 1440分钟)


# Build Information
version: "dev"
build_time: ""
git_commit: ""

services:
  # Health check settings
  healthCheck:
      interval: "30s"           # Health check interval
      timeout: "5s"             # Health check timeout
      retries: 3                # Number of retries before marking unhealthy

# 防抖动配置
debounce:
  enabled: true         # 是否启用防抖动
  windowMs: 500        # 防抖动窗口（毫秒）
  keyPrefix: "debounce:" # Redis键前缀

# 频率限制配置
rateLimit:
  enableRedis: true       # 是否使用Redis存储（分布式环境必须开启）
  shortWindow: 1          # 短时间窗口（秒）
  longWindow: 5           # 长时间窗口（秒）
  blockDuration: 60       # 封禁时长（秒）
  shortLimit: 2           # 短时间窗口内最大操作次数
  longLimit: 4            # 长时间窗口内最大操作次数

# 用户同步特定配置
userSync:
  # 消息拦截器集成配置
  messageInterceptor:
    enableSync: true # 是否在消息处理中启用用户同步检查
    asyncMode: true # 是否异步执行同步检查
    
  # 批量更新配置
  batchUpdate:
    loginTimeBatchSize: 100 # 登录时间批量大小，默认100
    batchFlushInterval: "30s" # 批量刷新间隔，默认30s
    maxBatchWaitTime: "5m" # 最大等待时间，默认5分钟
  
  # 缓存配置
  cache:
    userDataTTL: "10m" # 用户数据缓存TTL，默认10分钟
    compareFields: # 需要比较的字段列表
      - "nickname"
      - "name" 
      - "telegram_username"
      - "first_name"
      - "last_login_time"
    
  # 性能配置
  performance:
    enableAsyncProcessing: true # 启用异步处理
    maxConcurrentUpdates: 50 # 最大并发更新数

# 红包封面处理器配置
redPacketCover:
  processor:
    enabled: true
    batchSize: 10
    maxRetryAttempts: 3
    retryDelaySeconds: 300
    processingTimeoutSeconds: 60
    maxFileSizeMB: 2
    minDimension: 200
    maxDimension: 1000


# Consul 配置（用于配置同步）
consul:
  address: "127.0.0.1:8500" # Consul 服务器地址
  token: "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874" # ACL Token（生产环境请修改）
  config_prefix: "xpay/config" # 配置存储前缀
  watchRetryDelay: "2s" # Watch失败重试延迟（默认5秒，现在改为2秒）
